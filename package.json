{"name": "theclickfunnel", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.0.17", "axios": "^1.8.4", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "framer-motion": "^12.6.2", "gsap": "^3.12.7", "lenis": "^1.2.3", "lucide-react": "^0.484.0", "prop-types": "^15.8.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-native-svg": "^15.11.2", "react-router-dom": "^7.4.1", "react-scroll": "^1.9.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.0.17"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}