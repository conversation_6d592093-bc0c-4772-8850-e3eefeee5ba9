import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

const Preloader = ({ onComplete }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isFadingOut, setIsFadingOut] = useState(false);

  useEffect(() => {
    // Start fade-out animation 2 seconds after fill completes (fill takes 2.5s)
    const fadeOutTimer = setTimeout(() => {
      setIsFadingOut(true);
    }, 4500); // 2.5s fill + 2s wait = 4.5s

    const timer = setTimeout(() => {
      setIsLoading(false);
      if (onComplete) onComplete();
    }, 5000); // Total animation time: 5s (2.5s fill + 2s wait + 0.5s fade)

    return () => {
      clearTimeout(fadeOutTimer);
      clearTimeout(timer);
    };
  }, [onComplete]);

  if (!isLoading) return null;

  return (
    <AnimatePresence>
      {isLoading && (
        <motion.div
          className="fixed inset-0 bg-[#131313] flex flex-col items-center justify-center overflow-hidden z-50"
          initial={{ opacity: 1 }}
          animate={{ opacity: isFadingOut ? 0 : 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="relative w-64 h-64 sm:w-80 sm:h-80">
            <svg
              viewBox="0 0 1157 1009"
              className="absolute inset-0 w-full h-full"
              preserveAspectRatio="xMidYMid meet"
            >
              <defs>
                {/* Wave pattern */}
                <pattern
                  id="water"
                  width="0.25"
                  height="1.1"
                  patternContentUnits="objectBoundingBox"
                >
                  <path
                    fill="#fff"
                    d="M0.25,1H0c0,0,0-0.659,0-0.916c0.083-0.303,0.158,0.334,0.25,0C0.25,0.327,0.25,1,0.25,1z"
                  />
                </pattern>

                {/* Logo path for masking */}
                <path
                  id="logo-path"
                  d="M500.168 2.08522C418.155 5.6902 353.49 21.6873 288.376 53.6814C185.634 104.602 104.522 186.39 52.9257 291.385C4.25852 390.296 -11.0626 497.319 7.8635 606.82C33.5489 755.75 131.785 889.585 271.928 965.966L289.277 975.429L295.811 965.74C303.471 954.7 337.043 901.977 397.651 806.22C420.858 769.72 451.501 721.503 465.695 699.197C517.292 618.311 565.959 541.254 572.493 530.214L579.027 518.949L579.703 763.862L580.153 1009H664.194H748.461L749.812 927.437C750.714 882.601 751.39 797.658 751.39 738.627V631.153L665.771 574.15L580.153 517.146L575.196 521.427C569.564 526.384 511.884 579.107 432.575 651.882C403.059 679.145 373.318 706.407 366.784 712.265C352.815 725.108 353.49 725.333 330.734 702.126C310.005 681.173 300.993 669.456 288.376 647.151C248.045 576.628 243.088 493.488 274.632 415.08C296.036 361.456 346.281 306.931 395.849 283.499L410.044 276.739L411.17 340.728L412.297 404.716L494.085 459.241C539.147 489.208 576.323 513.316 576.999 512.865C577.45 512.189 578.576 397.281 579.477 257.137L580.829 2.53585L565.283 1.18398C556.721 0.282737 546.807 -0.167885 542.977 0.0574259C539.372 0.0574259 519.995 0.95867 500.168 2.08522ZM869.227 55.2588C866.749 58.6384 843.767 95.5894 817.856 137.047C792.171 178.504 746.883 251.054 717.593 298.144C688.303 345.234 649.774 406.969 631.975 435.584C614.175 463.973 595.7 493.263 590.743 500.699C585.786 507.909 582.857 513.541 583.984 513.091C585.335 512.64 630.848 470.056 685.148 418.46C739.673 367.089 789.017 320.45 794.875 315.042L805.69 305.129L822.363 322.478C875.311 377.228 901.221 435.809 906.854 513.316C908.206 530.665 909.107 643.32 909.107 775.803L908.882 1007.87L1018.83 1008.55C1079.22 1009 1135.09 1008.55 1142.98 1007.87L1156.95 1006.3V737.951C1156.95 484.026 1156.72 467.803 1152.67 439.639C1131.04 293.187 1049.48 165.886 921.499 78.6911C909.107 70.1293 893.336 59.9903 886.126 56.16L873.508 48.9501L869.227 55.2588Z"
                />

                <mask id="logo-mask">
                  <use xlinkHref="#logo-path" opacity="1" fill="white" />
                </mask>
              </defs>

              {/* Black logo outline */}
              <use xlinkHref="#logo-path" fill="#131313" />

              {/* Animated water fill */}
              <rect
                className="water-fill"
                mask="url(#logo-mask)"
                fill="url(#water)"
                x="-400"
                y="0"
                width="1600"
                height="1009"
                style={{
                  animation: 
                    "wave 0.7s infinite linear, fill-up 2.5s forwards ease-out",
                }}
              />
            </svg>

            {/* CSS for the animations */}
            <style jsx>{`
              @keyframes wave {
                0% { x: -400px; }
                100% { x: 0; }
              }
              
              @keyframes fill-up {
                0% {
                  height: 0;
                  y: 1009px;
                }
                100% {
                  height: 1009px;
                  y: 0;
                }
              }
            `}</style>
          </div>

          {/* Large Text "Creative Madness" */}
          <motion.h1
            className="hidden md:flex absolute text-white uppercase font-bold leading-none tracking-tight"
            style={{
              fontSize: "11vw",
              bottom: "0%",
              left: "0%",
              transform: "translateX(-50%)",
              whiteSpace: "nowrap",
            }}
            initial={{ y: "0%" }}
            animate={{ y: isFadingOut ? "100%" : "0%" }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
          >
            CreativeMadness
          </motion.h1>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Preloader;